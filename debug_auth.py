#!/usr/bin/env python3
"""
Debug Authentication Script

This script helps debug the current authentication state and group membership.
"""

import requests
import json

def test_auth_endpoints():
    print("🔍 AUTHENTICATION DEBUG REPORT")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # Test userinfo endpoint
    print("\n1. Testing /auth/userinfo endpoint:")
    print("-" * 40)
    try:
        response = requests.get(f"{base_url}/auth/userinfo")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test access-token endpoint
    print("\n2. Testing /auth/access-token endpoint:")
    print("-" * 40)
    try:
        response = requests.get(f"{base_url}/auth/access-token")
        print(f"Status Code: {response.status_code}")
        if response.status_code == 401:
            print("✅ Correctly requires authentication")
        else:
            print(f"Response: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test login endpoint
    print("\n3. Testing /auth/login endpoint:")
    print("-" * 40)
    try:
        response = requests.get(f"{base_url}/auth/login", allow_redirects=False)
        print(f"Status Code: {response.status_code}")
        if response.status_code in [302, 307, 308]:
            print(f"✅ Correctly redirects to OAuth provider")
            print(f"Location: {response.headers.get('location', 'Not found')}")
        else:
            print(f"Response: {response.text[:200]}...")
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n4. Frontend localStorage Debug:")
    print("-" * 40)
    print("To debug frontend authentication:")
    print("1. Open browser console")
    print("2. Run: console.log('auth_status:', localStorage.getItem('auth_status'))")
    print("3. Run: console.log('user_info:', JSON.parse(localStorage.getItem('user_info') || '{}'))")
    
    print("\n5. Expected vs Actual Flow:")
    print("-" * 40)
    print("✅ Expected: /auth/token returns { success: true, user: {...}, message: '...' }")
    print("❌ If you see tokens in response, the old server is still running")
    print("✅ Expected: User groups should include 'admins' for admin access")
    print("❌ If 'Access Restricted' appears, check user groups in localStorage")

if __name__ == "__main__":
    test_auth_endpoints()
