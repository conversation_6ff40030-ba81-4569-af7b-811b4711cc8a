/**
 * Secure authentication utilities
 * Provides secure access to authentication tokens when needed for API calls
 */

import { debug, error } from './logger';

/**
 * Securely fetch access token from backend session
 * Only use this for API calls that specifically require an access token
 * @returns {Promise<string|null>} Access token or null if not available
 */
export async function getAccessToken() {
  try {
    const response = await fetch('/auth/access-token', {
      method: 'GET',
      credentials: 'include' // Include session cookies
    });

    if (response.ok) {
      const data = await response.json();
      debug('Access token retrieved securely');
      return data.access_token;
    } else if (response.status === 401) {
      debug('Not authenticated - no access token available');
      return null;
    } else {
      error('Failed to retrieve access token:', { status: response.status });
      return null;
    }
  } catch (err) {
    error('Error retrieving access token:', { error: err });
    return null;
  }
}

// Cache for userinfo to prevent duplicate API calls
let userInfoCache = null;
let userInfoPromise = null;
let lastUserInfoCheck = 0;
const USER_INFO_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Get user info from backend with caching to prevent duplicate calls
 * @param {boolean} forceRefresh - Force refresh from backend
 * @returns {Promise<object>} User info object with authenticated and user properties
 */
async function getUserInfoFromBackend(forceRefresh = false) {
  const now = Date.now();

  // Return cached data if available and not expired
  if (!forceRefresh && userInfoCache && (now - lastUserInfoCheck) < USER_INFO_CACHE_DURATION) {
    debug('Returning cached user info');
    return userInfoCache;
  }

  // If there's already a pending request, return that promise
  if (userInfoPromise) {
    debug('Returning existing userinfo promise');
    return userInfoPromise;
  }

  // Create new request
  userInfoPromise = fetch('/auth/userinfo', {
    method: 'GET',
    credentials: 'include'
  })
  .then(response => {
    if (response.ok) {
      return response.json();
    }
    throw new Error(`HTTP ${response.status}`);
  })
  .then(data => {
    userInfoCache = data;
    lastUserInfoCheck = now;
    debug('User info fetched and cached');
    return data;
  })
  .catch(err => {
    error('Error fetching user info:', { error: err });
    return { authenticated: false, user: null };
  })
  .finally(() => {
    userInfoPromise = null; // Clear the promise
  });

  return userInfoPromise;
}

/**
 * Check if user is authenticated based on session
 * @returns {Promise<boolean>} True if authenticated, false otherwise
 */
export async function isAuthenticated() {
  try {
    const data = await getUserInfoFromBackend();
    return data.authenticated === true;
  } catch (err) {
    error('Error checking authentication status:', { error: err });
    return false;
  }
}

/**
 * Get current user information from session
 * @returns {Promise<object|null>} User info or null if not authenticated
 */
export async function getCurrentUser() {
  try {
    const data = await getUserInfoFromBackend();
    if (data.authenticated && data.user) {
      return data.user;
    }
    return null;
  } catch (err) {
    error('Error getting current user:', { error: err });
    return null;
  }
}

/**
 * Get both authentication status and user info in one call
 * @param {boolean} forceRefresh - Force refresh from backend
 * @returns {Promise<object>} Object with authenticated and user properties
 */
export async function getUserInfo(forceRefresh = false) {
  try {
    return await getUserInfoFromBackend(forceRefresh);
  } catch (err) {
    error('Error getting user info:', { error: err });
    return { authenticated: false, user: null };
  }
}

/**
 * Clear local authentication state and cache
 * Use this when logging out or when authentication becomes invalid
 */
export function clearLocalAuthState() {
  localStorage.removeItem('user_info');
  localStorage.removeItem('auth_status');
  // Clear the cache as well
  userInfoCache = null;
  userInfoPromise = null;
  lastUserInfoCheck = 0;
  debug('Local authentication state and cache cleared');
}

/**
 * Set local authentication state
 * Use this after successful authentication
 * @param {object} userInfo - User information to store
 */
export function setLocalAuthState(userInfo) {
  localStorage.setItem('user_info', JSON.stringify(userInfo));
  localStorage.setItem('auth_status', 'authenticated');
  debug('Local authentication state set');
}
