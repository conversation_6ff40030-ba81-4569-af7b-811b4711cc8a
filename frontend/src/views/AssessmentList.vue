<template>
  <PhantomLayout
    title="Assessments"
    :noScroll="true"
  >
    <div class="p-6 -mt-10">
      <!-- Removed the top action bar as we'll move the button next to search -->

      <!-- Loading indicator -->
      <div v-if="isLoading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-phantom-blue"></div>
        <span class="ml-3 text-white/80">Loading assessments...</span>
      </div>

      <!-- Error message -->
      <div v-if="message && !isSuccess" class="bg-red-500/10 backdrop-blur-sm border border-red-500/30 text-white px-6 py-4 rounded-xl mb-6 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
        {{ message }}
      </div>

      <!-- Success message -->
      <div v-if="message && isSuccess" class="bg-green-500/10 backdrop-blur-sm border border-green-500/30 text-white px-6 py-4 rounded-xl mb-6 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
        </svg>
        {{ message }}
      </div>

      <!-- Search bar with Add Assessment button - Always visible -->
      <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div class="w-full flex flex-row items-center">
          <div class="relative flex-1 mr-3">
            <input
              type="text"
              v-model="searchQuery"
              placeholder="Search assessments..."
              class="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 pl-10 text-white focus:outline-none focus:ring-2 focus:ring-phantom-blue/50"
            />
            <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
          <!-- Add Assessment button moved next to search bar -->
          <button
            @click="navigateToCreateAssessment"
            class="btn-phantom px-5 py-2.5 text-sm whitespace-nowrap"
          >
            <span class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              Add Assessment
            </span>
          </button>
        </div>
      </div>

      <!-- No assessments message -->
      <div v-if="!isLoading && !message && assessments.length === 0" class="text-center py-16">
        <div class="w-20 h-20 mx-auto mb-6 rounded-full bg-white/5 flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-white/40" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
        </div>
        <h3 class="text-xl font-medium text-white mb-2">No assessments found</h3>
        <p class="text-white/60 mb-6">Get started by creating your first assessment</p>
      </div>

      <!-- Assessments table -->
      <div v-if="!isLoading && assessments.length > 0">

        <div class="rounded-xl border border-white/10 backdrop-blur-sm">
          <table class="w-full text-left">
            <thead>
              <tr class="border-b border-white/10 bg-white/5">
                <th class="py-4 px-6 text-white/80 font-medium">Name</th>
                <th class="py-4 px-6 text-white/80 font-medium">Mode</th>
                <th class="py-4 px-6 text-white/80 font-medium">Questions</th>
                <th class="py-4 px-6 text-white/80 font-medium">Duration</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(assessment, index) in paginatedAssessments" :key="assessment.id"
                  :class="index % 2 === 0 ? 'bg-white/[0.03]' : 'bg-transparent'"
                  class="border-b border-white/5 hover:bg-white/[0.05] transition-colors duration-150 cursor-pointer"
                  @click="navigateToAssessmentDetail(assessment)">
                <td class="py-4 px-6 text-white font-medium">{{ assessment.name }}</td>
                <td class="py-4 px-6">
                  <span
                    class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium"
                    :class="assessment.question_selection_mode === 'fixed'
                      ? 'bg-phantom-indigo/20 text-phantom-indigo border border-phantom-indigo/30'
                      : 'bg-phantom-blue/20 text-phantom-blue border border-phantom-blue/30'"
                  >
                    {{ assessment.question_selection_mode === 'fixed' ? 'Fixed' : 'Dynamic' }}
                  </span>
                </td>
                <td class="py-4 px-6 text-white/80">
                  <div class="flex items-center">
                    <span
                      :title="`${assessment.total_questions || 0} questions`"
                      class="cursor-help"
                    >
                      {{ assessment.total_questions || 'N/A' }}
                    </span>
                  </div>
                </td>
                <td class="py-4 px-6 text-white/80">
                  {{ assessment.duration_minutes ? `${assessment.duration_minutes} min` : 'N/A' }}
                </td>
              </tr>
            </tbody>
          </table>

          <!-- Pagination Controls -->
          <div v-if="displayTotalPages > 1" class="mt-6">
            <Pagination
              :current-page="currentPage"
              :total-pages="displayTotalPages"
              :total-items="searchQuery ? filteredAssessments.length : totalItems"
              :items-per-page="itemsPerPage"
              @page-change="onPageChange"
            />
          </div>
        </div>
      </div>
    </div>
  </PhantomLayout>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { api } from '@/services/api';
import { getErrorMessage, logError } from '@/utils/errorHandling';
import { useMessageHandler } from '@/utils/messageHandler';
import { getAssessmentHashId } from '@/utils/hashIds';
import { extractResponseData, extractErrorInfo, extractPaginationMeta } from '@/utils/apiResponseHandler';

import PhantomLayout from '@/components/layout/Layout.vue';
import { Pagination } from '@/components/ui/pagination';

const router = useRouter();
const navigateTo = (path) => {
  router.push(path);
};



// Message handling
const { message, isSuccess, setErrorMessage, clearMessage } = useMessageHandler();

// Component state
const assessments = ref([]);
const allAssessments = ref([]); // For search functionality
const isLoading = ref(true);
const searchQuery = ref('');

// Pagination - Server-side pagination
const currentPage = ref(1);
const itemsPerPage = 7; // Exactly 7 items per page as required
const totalItems = ref(0);
const totalPages = ref(0);
const isSearching = ref(false);

// Computed properties for display
const filteredAssessments = computed(() => {
  // If no search query, return current assessments (server-side paginated)
  if (!searchQuery.value || searchQuery.value.trim() === '') {
    return assessments.value;
  }

  // When searching, use all assessments and filter client-side
  const query = searchQuery.value.toLowerCase().trim();
  const dataToFilter = allAssessments.value.length > 0 ? allAssessments.value : assessments.value;

  const filtered = dataToFilter.filter(assessment => {
    if (!assessment) return false;

    const nameMatch = assessment.name && assessment.name.toLowerCase().includes(query);
    const modeMatch = assessment.question_selection_mode && assessment.question_selection_mode.toLowerCase().includes(query);

    return nameMatch || modeMatch;
  });

  return filtered;
});

const paginatedAssessments = computed(() => {
  if (!searchQuery.value || searchQuery.value.trim() === '') {
    // No search - use server-side paginated data
    return assessments.value;
  }

  // Search active - paginate filtered results client-side
  const startIndex = (currentPage.value - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  return filteredAssessments.value.slice(startIndex, endIndex);
});

// Update totalPages computation
const displayTotalPages = computed(() => {
  if (!searchQuery.value || searchQuery.value.trim() === '') {
    return totalPages.value;
  }

  // For search results, calculate pages from filtered results
  return Math.ceil(filteredAssessments.value.length / itemsPerPage);
});

const goToPage = (page) => {
  currentPage.value = page;
  if (!searchQuery.value) {
    // Fetch new page from server only when not searching
    fetchAssessments();
  }
};

const nextPage = () => {
  const maxPages = searchQuery.value ? displayTotalPages.value : totalPages.value;
  if (currentPage.value < maxPages) {
    currentPage.value++;
    if (!searchQuery.value) {
      fetchAssessments();
    }
  }
};

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
    if (!searchQuery.value) {
      fetchAssessments();
    }
  }
};

// New pagination event handler for the Pagination component
const onPageChange = (page) => {
  currentPage.value = page;
  if (!searchQuery.value) {
    fetchAssessments();
  }
};

// Fetch assessments from API with server-side pagination
const fetchAssessments = async () => {
  isLoading.value = true;
  clearMessage();

  try {
    const offset = (currentPage.value - 1) * itemsPerPage;
    const params = {
      limit: itemsPerPage,
      offset: offset
    };

    const response = await api.admin.getAssessments(params);
    const data = extractResponseData(response);
    const paginationMeta = extractPaginationMeta(response);

    // Handle both new paginated format and legacy format
    if (data) {
      // Check if the data is already an array (new format) or has an assessments property (old format)
      if (Array.isArray(data)) {
        assessments.value = data;
      } else if (data.assessments) {
        assessments.value = data.assessments;
      } else {
        assessments.value = [];
      }
    } else {
      assessments.value = [];
    }

    // Update pagination metadata from server
    if (paginationMeta) {
      totalItems.value = paginationMeta.total;
      totalPages.value = Math.ceil(paginationMeta.total / itemsPerPage);
    }

    if (!assessments.value || assessments.value.length === 0) {
      // No assessments found or empty array returned
    }
  } catch (error) {
    logError(error, 'fetchAssessments');
    const errorInfo = extractErrorInfo(error);
    setErrorMessage(errorInfo.message || 'An unexpected error occurred while fetching assessments');
  } finally {
    isLoading.value = false;
  }
};

// Fetch all assessments for search functionality
const fetchAllAssessments = async () => {
  try {
    let allData = [];
    let offset = 0;
    const limit = 100; // Maximum allowed by API
    let hasMore = true;

    // Fetch all assessments in batches
    while (hasMore) {
      const params = {
        limit: limit,
        offset: offset
      };

      const response = await api.admin.getAssessments(params);
      const data = extractResponseData(response);
      const paginationMeta = extractPaginationMeta(response);

      if (data && data.length > 0) {
        // Check if the data is already an array (new format) or has an assessments property (old format)
        if (Array.isArray(data)) {
          allData = allData.concat(data);
        } else if (data.assessments) {
          allData = allData.concat(data.assessments);
        }

        // Check if we have more data to fetch
        if (paginationMeta && paginationMeta.total) {
          hasMore = (offset + limit) < paginationMeta.total;
        } else {
          // If no pagination meta, check if we got a full batch
          hasMore = data.length === limit;
        }

        offset += limit;
      } else {
        hasMore = false;
      }
    }

    allAssessments.value = allData;
  } catch (error) {
    logError(error, 'fetchAllAssessments');
    allAssessments.value = [];
  }
};

// Navigation functions
const navigateToCreateAssessment = () => {
  navigateTo('/create-assessment');
};

const viewAssessmentDetails = (assessmentId) => {
  navigateTo(`/assessment/${assessmentId}`);
};

const navigateToAssessmentDetail = (assessment) => {
  const hashId = getAssessmentHashId(assessment);
  navigateTo(`/assessment/${hashId}`);
};

// Cache flag to prevent duplicate fetchAllAssessments calls
const allAssessmentsFetched = ref(false);

// Reset to first page when search query changes
watch(searchQuery, async (newQuery, oldQuery) => {
  currentPage.value = 1;

  if (newQuery && newQuery.trim() !== '') {
    // Started searching - fetch all assessments if not already fetched
    if (allAssessments.value.length === 0 && !allAssessmentsFetched.value) {
      try {
        allAssessmentsFetched.value = true; // Set flag before fetching to prevent duplicates
        await fetchAllAssessments();
      } catch (error) {
        allAssessmentsFetched.value = false; // Reset flag on error
        // If we can't fetch all assessments, we'll search within the current page data
      }
    }
    isSearching.value = true;
  } else if (!newQuery || newQuery.trim() === '') {
    // Stopped searching - go back to server-side pagination
    isSearching.value = false;
    await fetchAssessments();
  }
});

// Load assessments when component mounts
onMounted(() => {
  fetchAssessments();
});
</script>

<style scoped>
</style>
