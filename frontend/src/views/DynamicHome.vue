<template>
  <div>
    <!-- Show admin home if user has admin group -->
    <Home v-if="showAdminHome" />

    <!-- Show user home if user has employee group -->
    <UserHome v-if="showUserHome" />

    <!-- Show message if user has no relevant groups -->
    <div v-if="!showAdminHome && !showUserHome" class="min-h-screen flex items-center justify-center">
      <div class="text-center p-8 bg-gray-100 rounded-lg shadow-md">
        <h1 class="text-2xl font-bold text-gray-800 mb-4">Access Restricted</h1>
        <p class="text-gray-600">You don't have permission to access this application.</p>
        <p class="text-gray-600 mt-2">Please contact your administrator for assistance.</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { debug, info, warning, error } from '@/utils/logger';
import Home from './Home.vue';
import UserHome from './UserHome.vue';

// Track which components to show
const showAdminHome = ref(false);
const showUserHome = ref(false);

// Get group names from environment variables
const adminGroupName = import.meta.env.VITE_ADMIN_GROUP_NAME || 'admins';
const employeeGroupName = import.meta.env.VITE_EMPLOYEE_GROUP_NAME || 'employees';

// Function to determine which components to show based on user groups
const determineComponentVisibility = (userInfo) => {
    if (userInfo && userInfo.groups) {
      debug('User groups array', { groups: userInfo.groups });

      const hasAdminGroup = userInfo.groups.includes(adminGroupName);
      const hasEmployeeGroup = userInfo.groups.includes(employeeGroupName);

      debug('User group membership', {
        adminGroup: adminGroupName,
        hasAdminGroup,
        employeeGroup: employeeGroupName,
        hasEmployeeGroup
      });

      // Prioritize admin view if user has admin group
      if (hasAdminGroup) {
        info('Admin user detected, showing admin home');
        showAdminHome.value = true;
        showUserHome.value = false; // Don't show both interfaces
      }
      // Only show user home if user has employee group but not admin group
      else if (hasEmployeeGroup) {
        info('Employee user detected, showing user home');
        showUserHome.value = true;
        showAdminHome.value = false;
      }

      // Log which components will be shown
      debug('Final component visibility state', {
        showAdminHome: showAdminHome.value,
        showUserHome: showUserHome.value
      });
    }
};

onMounted(async () => {
  // First try to get user info from localStorage
  const userInfoStr = localStorage.getItem('user_info');
  if (userInfoStr) {
    try {
      const userInfo = JSON.parse(userInfoStr);
      debug('User info in DynamicHome from localStorage', { userInfo });
      determineComponentVisibility(userInfo);
    } catch (e) {
      error('Error parsing user info from localStorage', { error: e });
    }
  }

  // If we don't have user info or components aren't visible, try to get from backend
  if (!showAdminHome.value && !showUserHome.value) {
    try {
      const { getUserInfo } = await import('@/utils/auth.js');
      const data = await getUserInfo();

      if (data.authenticated && data.user) {
        debug('User info in DynamicHome from backend', { userInfo: data.user });
        determineComponentVisibility(data.user);
      }
    } catch (e) {
      error('Error getting user info from backend', { error: e });
    }
  }

  // Listen for auth state changes
  window.addEventListener('auth-state-changed', handleAuthStateChange);
});

// Function to handle auth state changes
const handleAuthStateChange = () => {
  debug('Auth state changed in DynamicHome, rechecking user info');
  const userInfoStr = localStorage.getItem('user_info');
  if (userInfoStr) {
    try {
      const userInfo = JSON.parse(userInfoStr);
      debug('Updated user info in DynamicHome', { userInfo });
      determineComponentVisibility(userInfo);
    } catch (e) {
      error('Error parsing updated user info', { error: e });
    }
  } else {
    // No user info, reset visibility
    showAdminHome.value = false;
    showUserHome.value = false;
  }
};

onUnmounted(() => {
  window.removeEventListener('auth-state-changed', handleAuthStateChange);
});
</script>
