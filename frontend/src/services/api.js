import axios from 'axios';
import { logApiRequest, logApiResponse, logApiError } from '@/utils/logger';

// Get API base URL from environment variables

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: '/api',
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// Request interceptor - can be used for adding auth tokens
apiClient.interceptors.request.use(
  config => {
    // Only log API requests in development mode or when debug is enabled
    const isDevelopment = import.meta.env.MODE === 'development';
    const isDebugEnabled = import.meta.env.VITE_DEBUG === 'true';

    if (isDevelopment || isDebugEnabled) {
      logApiRequest(config.method?.toUpperCase() || 'GET', config.url, config.data);
    }

    // Check if authentication is enabled from environment variables
    const authEnabled = import.meta.env.VITE_AUTH_ENABLED === 'true';

    // Add authentication headers if needed
    if (authEnabled) {
      // Always include credentials for session-based auth
      // Can be disabled via environment variable
      const withCredentials = import.meta.env.VITE_AUTH_WITH_CREDENTIALS !== 'false';
      if (withCredentials) {
        config.withCredentials = true;
      }

      // For specific API calls that need access tokens, they can be fetched
      // from the secure /auth/access-token endpoint when needed
      // This removes the security risk of storing tokens in localStorage
    }

    return config;
  },
  error => {
    logApiError('REQUEST', 'interceptor', error);
    return Promise.reject(error);
  }
);

// Response interceptor - for global error handling
apiClient.interceptors.response.use(
  response => {
    // Only log successful API responses in development mode or when debug is enabled
    const isDevelopment = import.meta.env.MODE === 'development';
    const isDebugEnabled = import.meta.env.VITE_DEBUG === 'true';

    if (isDevelopment || isDebugEnabled) {
      logApiResponse(
        response.config?.method?.toUpperCase() || 'GET',
        response.config?.url || 'unknown',
        response.status,
        response.data
      );
    }
    return response;
  },
  error => {
    // Always log API errors as they are important for debugging
    logApiError(
      error.config?.method?.toUpperCase() || 'GET',
      error.config?.url || 'unknown',
      error
    );

    // Handle common errors globally
    if (error.response) {
      // Server responded with an error status

      // Check if authentication is enabled from environment variables
      const authEnabled = import.meta.env.VITE_AUTH_ENABLED === 'true';

      // Handle authentication errors
      if (authEnabled && error.response.status === 401) {
        // Unauthorized - redirect to login
        const loginUrl = import.meta.env.VITE_AUTH_LOGIN_URL || '/auth/login';
        window.location.href = loginUrl;
      }

      // Handle forbidden errors
      if (authEnabled && error.response.status === 403) {
        // Forbidden - redirect to access denied page if configured
        const accessDeniedUrl = import.meta.env.VITE_AUTH_ACCESS_DENIED_URL;
        if (accessDeniedUrl) {
          window.location.href = accessDeniedUrl;
        }
      }
    } else if (error.request) {
      // Request was made but no response received
      logApiError('REQUEST', 'no-response', error);
    } else {
      // Something else happened while setting up the request
      logApiError('REQUEST', 'setup-error', error);
    }

    return Promise.reject(error);
  }
);

/**
 * Centralized API request handler
 * @param {string} method - HTTP method (get, post, put, delete)
 * @param {string} url - API endpoint
 * @param {Object} data - Request payload (for POST, PUT)
 * @param {Object} params - URL parameters (for GET)
 * @param {Object} options - Additional axios options
 * @returns {Promise} - Axios promise
 */
const apiRequest = (method, url, data = null, params = null, options = {}) => {
  const config = {
    method,
    url,
    ...options
  };

  if (data) {
    config.data = data;
  }

  if (params) {
    config.params = params;
  }

  return apiClient(config);
};

// Export the axios instance
export default apiClient;

// Export the centralized request handler
export const request = {
  get: (url, params = null, options = {}) => apiRequest('get', url, null, params, options),
  post: (url, data = null, options = {}) => apiRequest('post', url, data, null, options),
  put: (url, data = null, options = {}) => apiRequest('put', url, data, null, options),
  delete: (url, data = null, options = {}) => apiRequest('delete', url, data, null, options),
  patch: (url, data = null, options = {}) => apiRequest('patch', url, data, null, options)
};

// Export common API functions
export const api = {
  // Auth endpoints
  auth: {
    login: () => {
      const loginUrl = import.meta.env.VITE_AUTH_LOGIN_URL || '/auth/login';
      window.location.href = loginUrl;
    },
    logout: () => {
      const logoutUrl = import.meta.env.VITE_AUTH_LOGOUT_URL || '/auth/logout';
      window.location.href = logoutUrl;
    },
    getUserInfo: async (forceRefresh = false) => {
      // Use the consolidated getUserInfo function from auth utils
      const { getUserInfo } = await import('@/utils/auth.js');
      return getUserInfo(forceRefresh);
    },
    getCurrentUserId: () => {
      // Get user info from localStorage
      const userInfoStr = localStorage.getItem('user_info');
      if (!userInfoStr) return null;

      try {
        const userInfo = JSON.parse(userInfoStr);
        return userInfo.id || userInfo.sub || null;
      } catch (error) {
        logApiError('GET', 'parse-user-info', error);
        return null;
      }
    },
  },

  // Admin endpoints
  admin: {
    // Assessments
    getAssessments: (params = {}) => request.get('/admin/assessments', params),
    getAssessment: (id, includeQuestions = true) => request.get(`/admin/assessment/${id}?include_questions=${includeQuestions}`),
    createAssessment: (data) => request.post('/admin/quiz', data),
    getAssessmentQuestions: (id) => request.get(`/admin/assessment-questions/${id}`),
    addFinalQuestions: (data) => request.post('/admin/final-questions', data),

    // Skills
    getSkills: (params = {}) => request.get('/admin/skills', params),
    getSkill: (id) => request.get(`/admin/skills/${id}`),
    createSkill: (data) => request.post('/admin/skills', data),
    suggestSkillDescription: (data) => request.post('/admin/suggest-skill-description', data),
    getSkillQuestions: (skillId) => request.get(`/admin/skill-questions/${skillId}`),
    generateSkillQuestions: (data) => request.post('/admin/generate-skill-questions', data, { timeout: 320000 }),
    getSkillQuestionCounts: () => request.get('/admin/skill-question-counts'),

    // Sessions
    getSessions: (params = {}) => request.get('/admin/sessions', params),
    getSessionDetails: (sessionId) => request.get(`/admin/sessions/${sessionId}/details`),
    createSession: (data) => request.post('/admin/sessions', data),
    getSessionUser: (code) => request.get(`/admin/sessions/${code}/user`), // code must be a 6-digit session code
    getAssessmentsWithSessions: () => request.get('/admin/assessments-with-sessions'),
    generateLink: (data) => request.post('/admin/generate-link', data),

    // Reports
    generateReport: (data) => request.post('/admin/reports', data),
    getUserWiseReport: (params) => request.get('/admin/reports/user-wise', params),
    getSkillwiseHeatmap: () => request.get('/admin/reports/skillwise-heatmap'),
    getAssessmentWiseReport: (params) => request.get('/admin/reports/assessment-wise', params),

    // Users
    getUsers: () => request.get('/admin/users'),
    getUserAssessments: (userId) => request.get(`/admin/users/${userId}/assessments`),
    getUserSkillPerformance: (userId) => request.get(`/admin/users/${userId}/skill-performance`),
  },

  user: {
    getUserAssessmentsByEmail: (email) => request.get(`/user/${email}/assessments`),
    getUserSkillPerformanceByEmail: (email) => request.get(`/user/${email}/skill-performance`),
    getUserSessions: (email) => request.get(`/user/${email}/sessions`, null, { headers: { 'X-Debug': 'true' } }),
  },

  // Quiz endpoints
  quiz: {
    validateSessionCode: (data) => request.post('/validate_session_code', data),
    startSession: (data) => request.post('/start_session', data),
    getQuestions: (sessionCode, params) => request.get(`/get_questions/${sessionCode}`, params),
    checkAndSaveAnswer: (data) => request.post('/check_and_save_answer', data),
    submitSession: (data) => request.post('/submit_session', data),
    pauseSession: (data) => request.post('/pause_session', data),
    resumeSession: (data) => request.post('/resume_session', data),
    getPauseStatus: (sessionCode) => request.get(`/pause_status/${sessionCode}`),
  }
};
