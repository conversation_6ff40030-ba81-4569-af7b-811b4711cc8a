#!/usr/bin/env python3
"""
Test script to debug the OAuth authentication flow
"""
import requests
import json
import base64
from urllib.parse import urlencode, parse_qs, urlparse

def test_auth_flow():
    """Test the complete OAuth flow"""
    
    # Step 1: Get authorization URL
    auth_params = {
        'client_id': 'example-app',
        'redirect_uri': 'http://localhost:5173/callback',
        'response_type': 'code',
        'scope': 'openid email profile',
        'state': 'test123'
    }
    
    auth_url = f"http://localhost:5556/auth?{urlencode(auth_params)}"
    print(f"1. Authorization URL: {auth_url}")
    print("   Please visit this URL in your browser and complete the login")
    print("   Then paste the callback URL here:")
    
    # Get the callback URL from user
    callback_url = input("Callback URL: ").strip()
    
    # Parse the authorization code from callback URL
    parsed_url = urlparse(callback_url)
    query_params = parse_qs(parsed_url.query)
    
    if 'code' not in query_params:
        print("Error: No authorization code found in callback URL")
        return
    
    auth_code = query_params['code'][0]
    print(f"2. Authorization code: {auth_code}")
    
    # Step 2: Exchange code for tokens
    token_data = {
        'code': auth_code,
        'redirect_uri': 'http://localhost:5173/callback',
        'state': query_params.get('state', [''])[0]
    }
    
    print(f"3. Exchanging code for tokens...")
    print(f"   Token data: {json.dumps(token_data, indent=2)}")
    
    try:
        response = requests.post(
            'http://localhost:8002/auth/token',
            json=token_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"4. Token exchange response:")
        print(f"   Status: {response.status_code}")
        print(f"   Headers: {dict(response.headers)}")
        
        try:
            response_json = response.json()
            print(f"   Body: {json.dumps(response_json, indent=2)}")
        except:
            print(f"   Body (text): {response.text}")
            
    except Exception as e:
        print(f"Error during token exchange: {e}")

def test_direct_dex_token():
    """Test direct token exchange with Dex"""
    print("\n=== Testing direct Dex token exchange ===")
    
    # This would require a valid authorization code
    # For now, just test the endpoint accessibility
    try:
        response = requests.get('http://localhost:5556/keys')
        print(f"Dex JWKS endpoint: {response.status_code}")
        if response.status_code == 200:
            jwks = response.json()
            print(f"JWKS keys count: {len(jwks.get('keys', []))}")
    except Exception as e:
        print(f"Error accessing Dex JWKS: {e}")

if __name__ == "__main__":
    print("OAuth Authentication Flow Test")
    print("=" * 40)
    
    test_direct_dex_token()
    print()
    test_auth_flow()
