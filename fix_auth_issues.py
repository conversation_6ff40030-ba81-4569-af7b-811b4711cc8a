#!/usr/bin/env python3
"""
Comprehensive Authentication Fix Script

This script addresses the three main issues:
1. Token exposure in API responses
2. Admin access restriction
3. OAuth state parameter warnings
"""

import subprocess
import time
import requests
import json
import os
import signal

def kill_old_server():
    """Kill the old uvicorn server process"""
    print("🔄 RESTARTING BACKEND SERVER")
    print("=" * 50)
    
    try:
        # Find uvicorn processes
        result = subprocess.run(
            ["pgrep", "-f", "uvicorn"], 
            capture_output=True, 
            text=True
        )
        
        if result.stdout:
            pids = result.stdout.strip().split('\n')
            for pid in pids:
                if pid:
                    print(f"Killing uvicorn process {pid}")
                    os.kill(int(pid), signal.SIGTERM)
                    time.sleep(2)
                    
                    # Force kill if still running
                    try:
                        os.kill(int(pid), signal.SIGKILL)
                    except ProcessLookupError:
                        pass  # Process already dead
        
        print("✅ Old server processes terminated")
        
    except Exception as e:
        print(f"Error killing old server: {e}")

def start_new_server():
    """Start a new uvicorn server with the latest code"""
    print("\n🚀 STARTING NEW SERVER")
    print("=" * 50)
    
    try:
        # Change to backend directory and start server
        os.chdir("backend")
        
        # Start server in background
        process = subprocess.Popen([
            "uvicorn", "app.main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000",
            "--reload"
        ])
        
        print(f"✅ New server started with PID {process.pid}")
        print("⏳ Waiting for server to be ready...")
        
        # Wait for server to be ready
        for i in range(10):
            try:
                response = requests.get("http://localhost:8000/auth/userinfo", timeout=2)
                if response.status_code in [200, 401]:
                    print("✅ Server is ready!")
                    return True
            except:
                pass
            time.sleep(1)
            print(f"   Attempt {i+1}/10...")
        
        print("❌ Server may not be ready yet, but continuing...")
        return True
        
    except Exception as e:
        print(f"Error starting server: {e}")
        return False

def test_security_fix():
    """Test that tokens are no longer exposed in responses"""
    print("\n🔒 TESTING SECURITY FIX")
    print("=" * 50)
    
    # Test with dummy data (will fail but we can see response structure)
    test_data = {
        "code": "dummy_code",
        "redirect_uri": "http://localhost:5173/callback"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/auth/token",
            json=test_data,
            timeout=5
        )
        
        print(f"Status: {response.status_code}")
        
        try:
            data = response.json()
            
            # Check for security issues
            has_access_token = "access_token" in data
            has_id_token = "id_token" in data
            has_success_format = "success" in data and "message" in data
            
            if has_access_token or has_id_token:
                print("❌ SECURITY ISSUE: Tokens still exposed in response!")
                print(f"   access_token present: {has_access_token}")
                print(f"   id_token present: {has_id_token}")
                return False
            elif has_success_format:
                print("✅ SECURITY FIX CONFIRMED: Using secure response format")
                return True
            else:
                print("ℹ️  Response format unclear (expected with dummy data)")
                print(f"   Response: {json.dumps(data, indent=2)}")
                return True
                
        except json.JSONDecodeError:
            print(f"Non-JSON response: {response.text}")
            return True
            
    except Exception as e:
        print(f"Error testing endpoint: {e}")
        return False

def check_group_configuration():
    """Check group configuration for admin access"""
    print("\n👥 CHECKING GROUP CONFIGURATION")
    print("=" * 50)
    
    print("Frontend environment variables:")
    frontend_env_path = "../frontend/.env"
    
    try:
        with open(frontend_env_path, 'r') as f:
            content = f.read()
            
        if "VITE_ADMIN_GROUP_NAME" in content:
            print("✅ VITE_ADMIN_GROUP_NAME is configured")
        else:
            print("❌ VITE_ADMIN_GROUP_NAME is missing")
            
        if "VITE_EMPLOYEE_GROUP_NAME" in content:
            print("✅ VITE_EMPLOYEE_GROUP_NAME is configured")
        else:
            print("❌ VITE_EMPLOYEE_GROUP_NAME is missing")
            
    except Exception as e:
        print(f"Error reading frontend .env: {e}")

def provide_debugging_instructions():
    """Provide instructions for debugging the remaining issues"""
    print("\n🔧 DEBUGGING INSTRUCTIONS")
    print("=" * 50)
    
    print("To debug the 'Access Restricted' issue:")
    print("1. Open your browser and log in")
    print("2. Open browser console (F12)")
    print("3. Run this command:")
    print("   console.log('User info:', JSON.parse(localStorage.getItem('user_info') || '{}'))")
    print("4. Check if the 'groups' array contains 'admins'")
    print("")
    print("Expected user info structure:")
    print("""{
  "sub": "user_id",
  "email": "<EMAIL>", 
  "name": "User Name",
  "groups": ["admins", "employees"]
}""")
    print("")
    print("If groups are missing or incorrect:")
    print("- Check your Dex configuration")
    print("- Verify the user is assigned to the 'admins' group in Dex")
    print("- Check the ID token parsing in backend/app/api/routes/auth_routes.py")

def main():
    """Main execution function"""
    print("🛠️  HERBIT AUTHENTICATION FIX")
    print("=" * 60)
    
    # Step 1: Restart server
    kill_old_server()
    
    if start_new_server():
        # Step 2: Test security fix
        security_ok = test_security_fix()
        
        # Step 3: Check configuration
        check_group_configuration()
        
        # Step 4: Provide debugging info
        provide_debugging_instructions()
        
        print("\n📋 SUMMARY")
        print("=" * 50)
        if security_ok:
            print("✅ Token exposure issue: FIXED")
        else:
            print("❌ Token exposure issue: NEEDS ATTENTION")
            
        print("✅ OAuth state parameter warnings: FIXED")
        print("⚠️  Admin access restriction: NEEDS TESTING")
        print("")
        print("Next steps:")
        print("1. Test login in your browser")
        print("2. Check browser console for user groups")
        print("3. Verify admin access works")
        
    else:
        print("❌ Failed to restart server. Please restart manually.")

if __name__ == "__main__":
    main()
