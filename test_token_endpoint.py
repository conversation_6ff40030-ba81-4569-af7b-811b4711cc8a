#!/usr/bin/env python3
"""
Test Token Endpoint Script

This script tests the /auth/token endpoint to see what it's actually returning.
"""

import requests
import json

def test_token_endpoint():
    print("🧪 TESTING TOKEN ENDPOINT")
    print("=" * 50)
    
    # Test data (this won't work without a real OAuth code, but we can see the endpoint structure)
    test_data = {
        "code": "test_code_123",
        "redirect_uri": "http://localhost:5173/callback",
        "state": "test_state"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/auth/token",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        try:
            response_json = response.json()
            print(f"Response Body:")
            print(json.dumps(response_json, indent=2))
            
            # Check if tokens are exposed
            if "access_token" in response_json:
                print("\n❌ SECURITY ISSUE: access_token found in response!")
            if "id_token" in response_json:
                print("❌ SECURITY ISSUE: id_token found in response!")
            if "success" in response_json and response_json.get("success"):
                print("\n✅ SECURE: Using new secure response format")
            
        except json.JSONDecodeError:
            print(f"Response Text: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")

def check_server_processes():
    print("\n🔍 CHECKING SERVER PROCESSES")
    print("=" * 50)
    
    import subprocess
    try:
        # Check for Python processes running on port 8000
        result = subprocess.run(
            ["lsof", "-i", ":8000"], 
            capture_output=True, 
            text=True
        )
        print("Processes on port 8000:")
        print(result.stdout)
        
        # Check for uvicorn processes
        result = subprocess.run(
            ["pgrep", "-f", "uvicorn"], 
            capture_output=True, 
            text=True
        )
        if result.stdout:
            print(f"\nUvicorn processes: {result.stdout.strip()}")
        
    except Exception as e:
        print(f"Error checking processes: {e}")

if __name__ == "__main__":
    test_token_endpoint()
    check_server_processes()
    
    print("\n💡 TROUBLESHOOTING TIPS:")
    print("1. If you see tokens in the response, an old server version is running")
    print("2. Try restarting the backend server")
    print("3. Check if there are multiple Python processes running")
    print("4. Verify the auth_routes.py file has the secure return statement")
