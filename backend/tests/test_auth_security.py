"""
Test suite for authentication security
Verifies that sensitive tokens are not exposed in API responses
"""

import pytest
from fastapi.testclient import Test<PERSON><PERSON>
from unittest.mock import patch, MagicMock
import sys
import os

# Add the parent directory to the path to import the app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.main import app

client = TestClient(app)


class TestAuthSecurity:
    """Test authentication security measures"""

    @patch('app.api.routes.auth_routes.AsyncOAuth2Client')
    def test_token_exchange_no_sensitive_data_exposure(self, mock_oauth_client):
        """
        Test that the /auth/token endpoint does not expose sensitive tokens
        in the response payload
        """
        # Mock the OAuth2 client and token response
        mock_client_instance = MagicMock()
        mock_oauth_client.return_value = mock_client_instance
        
        # Mock token response from OAuth provider
        mock_token_response = {
            "access_token": "sensitive_access_token_12345",
            "id_token": "sensitive_id_token_67890",
            "token_type": "bearer",
            "expires_in": 3600
        }
        mock_client_instance.fetch_token.return_value = mock_token_response
        
        # Mock ID token parsing
        with patch('app.api.routes.auth_routes.parse_dex_id_token') as mock_parse:
            mock_parse.return_value = {
                "sub": "test_user_123",
                "email": "<EMAIL>",
                "name": "Test User"
            }
            
            # Make request to token exchange endpoint
            response = client.post(
                "/auth/token",
                json={
                    "code": "test_auth_code",
                    "redirect_uri": "http://localhost:5173/callback"
                }
            )
            
            # Verify response is successful
            assert response.status_code == 200
            
            response_data = response.json()
            
            # Verify that sensitive tokens are NOT in the response
            assert "access_token" not in response_data, "Access token should not be exposed in response"
            assert "id_token" not in response_data, "ID token should not be exposed in response"
            
            # Verify that only safe data is returned
            assert response_data.get("success") is True
            assert "user" in response_data
            assert response_data["user"]["email"] == "<EMAIL>"
            assert "message" in response_data

    def test_userinfo_endpoint_security(self):
        """
        Test that the /auth/userinfo endpoint only returns user info
        and authentication status, not sensitive tokens
        """
        # Test without session (should return not authenticated)
        response = client.get("/auth/userinfo")
        
        assert response.status_code == 200
        response_data = response.json()
        
        # Verify response structure
        assert "authenticated" in response_data
        assert "user" in response_data
        
        # Verify no sensitive tokens are exposed
        assert "access_token" not in response_data
        assert "id_token" not in response_data
        assert "token_type" not in response_data

    def test_access_token_endpoint_requires_authentication(self):
        """
        Test that the /auth/access-token endpoint requires authentication
        and only provides tokens to authenticated users
        """
        # Test without session (should return 401)
        response = client.get("/auth/access-token")
        
        assert response.status_code == 401
        response_data = response.json()
        assert "detail" in response_data
        assert response_data["detail"] == "Not authenticated"

    def test_callback_endpoint_security(self):
        """
        Test that the callback endpoint properly handles authentication
        without exposing sensitive data
        """
        # Test callback endpoint exists and handles requests properly
        response = client.get("/auth/callback")
        
        # Should redirect or handle the callback appropriately
        # The exact behavior depends on the implementation
        assert response.status_code in [200, 302, 307, 308]

    def test_login_endpoint_security(self):
        """
        Test that the login endpoint doesn't expose sensitive information
        """
        response = client.get("/auth/login")
        
        # Should redirect to OAuth provider or return appropriate response
        assert response.status_code in [200, 302, 307, 308]

    def test_logout_endpoint_security(self):
        """
        Test that the logout endpoint properly clears session data
        """
        # Test logout with JSON accept header
        response = client.get(
            "/auth/logout",
            headers={"Accept": "application/json"}
        )
        
        assert response.status_code == 200
        response_data = response.json()
        
        # Verify logout response doesn't contain sensitive data
        assert "access_token" not in response_data
        assert "id_token" not in response_data
        
        # Should contain success message
        assert "success" in response_data or "message" in response_data


if __name__ == "__main__":
    pytest.main([__file__])
