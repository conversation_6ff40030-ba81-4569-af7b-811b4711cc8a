import os
from typing import Optional

import requests
from authlib.integrations.httpx_client import Async<PERSON><PERSON>2<PERSON>lient
from authlib.integrations.starlette_client import OAuth
from authlib.jose import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jwt
from dotenv import load_dotenv
from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import RedirectResponse
from pydantic import BaseModel

from ...utils.logger import error as log_error
from ...utils.logger import info as log_info
from ...utils.logger import (
    log_auth_event,
)
from ...utils.logger import warning as log_warning

# Load environment variables
load_dotenv()

router = APIRouter(prefix="/auth", tags=["Authentication"])


# --- Token request model ---
class TokenRequest(BaseModel):
    code: str
    redirect_uri: str
    state: Optional[str] = None


# --- OAuth2 Client Setup ---
oauth = OAuth()
oauth_config = {
    "name": "dex",
    "client_id": os.getenv("AUTH_CLIENT_ID"),
    "client_secret": os.getenv("AUTH_CLIENT_SECRET"),
    "access_token_url": os.getenv("AUTH_TOKEN_URL"),
    "authorize_url": os.getenv("AUTH_AUTHORIZE_URL"),
    "client_kwargs": {"scope": "openid email profile"},
}
oauth.register(**oauth_config)

DEX_ISSUER = os.getenv("AUTH_ISSUER", "")
JWKS_URL = os.getenv("AUTH_JWKS_URI", f"{DEX_ISSUER}/keys")


# --- JWT Parser ---
def parse_dex_id_token(id_token: str) -> dict:
    try:
        log_info(f"Attempting to validate ID token. JWKS_URL: {JWKS_URL}, DEX_ISSUER: {DEX_ISSUER}")

        # Get JWKS
        jwks_response = requests.get(JWKS_URL)
        log_info(f"JWKS response status: {jwks_response.status_code}")
        jwks_data = jwks_response.json()
        log_info(f"JWKS data: {jwks_data}")

        jwks = JsonWebKey.import_key_set(jwks_data)
        claims = jwt.decode(id_token, key=jwks)
        log_info(f"Decoded claims: {dict(claims)}")

        claims.validate()  # Only checks exp, nbf, etc.
        log_info("Claims validation passed")

        # Manual issuer validation
        actual_issuer = claims.get("iss")
        log_info(f"Issuer validation: actual='{actual_issuer}', expected='{DEX_ISSUER}'")
        if actual_issuer != DEX_ISSUER:
            raise JoseError(f"Issuer mismatch: {actual_issuer} != {DEX_ISSUER}")

        log_auth_event("token_validation", success=True, user_id=claims.get("sub"))
        return dict(claims)
    except JoseError as e:
        log_error(f"JWT validation error: {e}")
        log_auth_event("token_validation", success=False, error=str(e))
        raise HTTPException(status_code=401, detail="Invalid ID token")
    except Exception as e:
        log_error(f"Unexpected error during token validation: {e}")
        log_auth_event("token_validation", success=False, error=str(e))
        raise HTTPException(status_code=401, detail="Invalid ID token")


# === OAuth Login Redirect ===
@router.get("/login")
async def login(request: Request):
    redirect_uri = os.getenv("AUTH_REDIRECT_URI")
    return await oauth.dex.authorize_redirect(request, redirect_uri)


# === Callback After Dex Login ===
@router.get("/callback")
async def callback(request: Request):
    try:
        token = await oauth.dex.authorize_access_token(request)
        user = await oauth.dex.parse_id_token(request, token)

        # Check if we have valid user information
        if not user:
            log_auth_event(
                "callback", success=False, error="No valid user information found"
            )
            return RedirectResponse(
                url=f"{os.getenv('FRONTEND_URL')}/login?error=no_user_info"
            )

        log_auth_event("callback", success=True, user_id=user.get("sub"))
        request.session["user"] = dict(user)
        return RedirectResponse(url=f"{os.getenv('FRONTEND_URL')}/")
    except Exception as e:
        log_auth_event("callback", success=False, error=str(e))
        return RedirectResponse(
            url=f"{os.getenv('FRONTEND_URL')}/login?error=authentication_failed"
        )


# === Check if User is Logged In ===
@router.get("/userinfo")
async def userinfo(request: Request):
    user = request.session.get("user")
    return {"authenticated": bool(user), "user": user}


# === Get Access Token for API Calls (Secure) ===
@router.get("/access-token")
async def get_access_token(request: Request):
    """
    Securely provides access token for authenticated API calls.
    Only returns token if user is authenticated via session.
    """
    user = request.session.get("user")
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    access_token = request.session.get("access_token")
    token_type = request.session.get("token_type", "bearer")

    if not access_token:
        raise HTTPException(status_code=401, detail="No valid access token found")

    return {
        "access_token": access_token,
        "token_type": token_type
    }


# === Logout ===
@router.get("/logout")
async def logout(request: Request):
    # Clear the user session
    request.session.pop("user", None)

    # Check if this is an API call (has Accept: application/json header)
    accept_header = request.headers.get("accept", "")
    if "application/json" in accept_header:
        # Return JSON response for API calls
        return {"success": True, "message": "Logged out successfully"}
    else:
        # Redirect to Dex logout for browser navigation
        return RedirectResponse(url=os.getenv("AUTH_LOGOUT_URL"))


# === Code-to-Token Exchange (for frontend) ===
@router.post("/token")
async def exchange_token(request_data: TokenRequest, request: Request):
    try:
        log_info("Token exchange request received")

        client = AsyncOAuth2Client(
            client_id=os.getenv("AUTH_CLIENT_ID"),
            client_secret=os.getenv("AUTH_CLIENT_SECRET"),
            token_endpoint=os.getenv("AUTH_TOKEN_URL"),
        )

        token = await client.fetch_token(
            url=os.getenv("AUTH_TOKEN_URL"),
            code=request_data.code,
            redirect_uri=request_data.redirect_uri,
        )

        id_token = token.get("id_token")
        user_info = {}

        if id_token:
            try:
                user_info = parse_dex_id_token(id_token)
                log_info("Successfully parsed user info from ID token")
            except Exception as e:
                log_warning(f"Failed to parse ID token: {e}")

        # If we couldn't get user info from the ID token and there's no fallback info in the token
        if not user_info and not (
            token.get("sub") or token.get("email") or token.get("name")
        ):
            log_error("No valid user information found in token")
            raise HTTPException(
                status_code=401,
                detail="Authentication failed: No valid user information found",
            )

        # Only use fallback if we have at least some basic info in the token
        if not user_info:
            user_info = {
                "sub": token.get("sub", "user"),
                "email": token.get("email", "<EMAIL>"),
                "name": token.get("name", "Authenticated User"),
            }
            log_warning("Using fallback user info from token")

        # Store tokens securely in session (full user info for internal use)
        request.session["user"] = user_info  # Full user info for backend use
        request.session["access_token"] = token.get("access_token")
        request.session["id_token"] = id_token
        request.session["token_type"] = token.get("token_type", "bearer")
        request.session["expires_in"] = token.get("expires_in", 3600)

        log_auth_event("token_exchange", success=True, user_id=user_info.get("sub"))

        # Return only safe, non-sensitive user information
        safe_user_info = {
            "email": user_info.get("email"),
            "name": user_info.get("name"),
            "groups": user_info.get("groups", []),
            "email_verified": user_info.get("email_verified", False)
        }

        # Return only success status and safe user info (no sensitive data)
        return {
            "success": True,
            "user": safe_user_info,
            "message": "Authentication successful"
        }

    except Exception as e:
        log_error("Error exchanging token", exception=e)
        log_auth_event("token_exchange", success=False, error=str(e))
        raise HTTPException(status_code=400, detail="Token exchange failed")


# === Setup Function for App ===
def setup_auth(app):
    app.include_router(router)
