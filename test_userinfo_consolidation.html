<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test UserInfo Consolidation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .network-log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>UserInfo Consolidation Test</h1>
        <p>This test verifies that multiple calls to userinfo-related functions only result in a single network request.</p>
        
        <div class="test-section info">
            <h3>Test Instructions:</h3>
            <ol>
                <li>Open browser developer tools (F12)</li>
                <li>Go to the Network tab</li>
                <li>Filter by "userinfo" or "auth"</li>
                <li>Click the test button below</li>
                <li>Verify that only ONE request to /auth/userinfo is made</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>Test: Multiple Simultaneous Calls</h3>
            <p>This will simulate multiple components calling userinfo functions at the same time.</p>
            <button onclick="testMultipleCalls()">Run Multiple Calls Test</button>
            <div id="multipleCallsResult"></div>
        </div>

        <div class="test-section">
            <h3>Test: Cached Calls</h3>
            <p>This will test that subsequent calls within the cache duration use cached data.</p>
            <button onclick="testCachedCalls()">Run Cached Calls Test</button>
            <div id="cachedCallsResult"></div>
        </div>

        <div class="test-section">
            <h3>Test: Force Refresh</h3>
            <p>This will test that force refresh bypasses the cache.</p>
            <button onclick="testForceRefresh()">Run Force Refresh Test</button>
            <div id="forceRefreshResult"></div>
        </div>

        <div class="test-section">
            <h3>Network Request Log</h3>
            <div id="networkLog" class="network-log">
                Network requests will be logged here...
            </div>
        </div>
    </div>

    <script>
        // Mock the auth utility functions for testing
        // In a real scenario, these would be imported from the actual auth.js file
        
        let networkRequests = [];
        let userInfoCache = null;
        let userInfoPromise = null;
        let lastUserInfoCheck = 0;
        const USER_INFO_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

        function logNetworkRequest(url, method = 'GET') {
            const timestamp = new Date().toISOString();
            const logEntry = `[${timestamp}] ${method} ${url}`;
            networkRequests.push(logEntry);
            
            const logElement = document.getElementById('networkLog');
            logElement.innerHTML = networkRequests.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
        }

        // Mock fetch function to simulate network requests
        async function mockFetch(url, options = {}) {
            logNetworkRequest(url, options.method || 'GET');
            
            // Simulate network delay
            await new Promise(resolve => setTimeout(resolve, 100));
            
            // Mock response
            return {
                ok: true,
                json: async () => ({
                    authenticated: true,
                    user: {
                        id: 'test-user',
                        email: '<EMAIL>',
                        name: 'Test User',
                        groups: ['employees']
                    }
                })
            };
        }

        // Mock getUserInfo function with caching
        async function getUserInfoFromBackend(forceRefresh = false) {
            const now = Date.now();
            
            // Return cached data if available and not expired
            if (!forceRefresh && userInfoCache && (now - lastUserInfoCheck) < USER_INFO_CACHE_DURATION) {
                console.log('Returning cached user info');
                return userInfoCache;
            }
            
            // If there's already a pending request, return that promise
            if (userInfoPromise) {
                console.log('Returning existing userinfo promise');
                return userInfoPromise;
            }
            
            // Create new request
            userInfoPromise = mockFetch('/auth/userinfo', {
                method: 'GET',
                credentials: 'include'
            })
            .then(response => {
                if (response.ok) {
                    return response.json();
                }
                throw new Error(`HTTP ${response.status}`);
            })
            .then(data => {
                userInfoCache = data;
                lastUserInfoCheck = now;
                console.log('User info fetched and cached');
                return data;
            })
            .catch(err => {
                console.error('Error fetching user info:', err);
                return { authenticated: false, user: null };
            })
            .finally(() => {
                userInfoPromise = null; // Clear the promise
            });
            
            return userInfoPromise;
        }

        async function isAuthenticated() {
            const data = await getUserInfoFromBackend();
            return data.authenticated === true;
        }

        async function getCurrentUser() {
            const data = await getUserInfoFromBackend();
            if (data.authenticated && data.user) {
                return data.user;
            }
            return null;
        }

        async function getUserInfo(forceRefresh = false) {
            return await getUserInfoFromBackend(forceRefresh);
        }

        // Test functions
        async function testMultipleCalls() {
            const resultDiv = document.getElementById('multipleCallsResult');
            resultDiv.innerHTML = '<p>Running test...</p>';
            
            // Clear previous requests
            networkRequests = [];
            userInfoCache = null;
            userInfoPromise = null;
            lastUserInfoCheck = 0;
            
            const startTime = Date.now();
            
            // Make multiple simultaneous calls
            const promises = [
                isAuthenticated(),
                getCurrentUser(),
                getUserInfo(),
                isAuthenticated(),
                getCurrentUser()
            ];
            
            try {
                const results = await Promise.all(promises);
                const endTime = Date.now();
                
                const userInfoRequests = networkRequests.filter(req => req.includes('/auth/userinfo'));
                
                if (userInfoRequests.length === 1) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Test PASSED</h4>
                            <p>Made 5 function calls but only 1 network request to /auth/userinfo</p>
                            <p>Time taken: ${endTime - startTime}ms</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Test FAILED</h4>
                            <p>Expected 1 network request, but got ${userInfoRequests.length}</p>
                            <p>Network requests: ${userInfoRequests.length}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Test ERROR</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testCachedCalls() {
            const resultDiv = document.getElementById('cachedCallsResult');
            resultDiv.innerHTML = '<p>Running test...</p>';
            
            // Clear previous requests
            networkRequests = [];
            
            // First call to populate cache
            await getUserInfo();
            const firstCallRequests = networkRequests.length;
            
            // Second call should use cache
            await getUserInfo();
            const secondCallRequests = networkRequests.length;
            
            if (secondCallRequests === firstCallRequests) {
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ Test PASSED</h4>
                        <p>Second call used cached data (no additional network request)</p>
                        <p>Total requests: ${secondCallRequests}</p>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Test FAILED</h4>
                        <p>Second call made additional network request</p>
                        <p>First call requests: ${firstCallRequests}, Second call requests: ${secondCallRequests}</p>
                    </div>
                `;
            }
        }

        async function testForceRefresh() {
            const resultDiv = document.getElementById('forceRefreshResult');
            resultDiv.innerHTML = '<p>Running test...</p>';
            
            // Clear previous requests
            networkRequests = [];
            
            // First call to populate cache
            await getUserInfo();
            const firstCallRequests = networkRequests.length;
            
            // Force refresh should bypass cache
            await getUserInfo(true);
            const forceRefreshRequests = networkRequests.length;
            
            if (forceRefreshRequests === firstCallRequests + 1) {
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ Test PASSED</h4>
                        <p>Force refresh bypassed cache and made new request</p>
                        <p>Total requests: ${forceRefreshRequests}</p>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Test FAILED</h4>
                        <p>Force refresh did not work as expected</p>
                        <p>Expected ${firstCallRequests + 1} requests, got ${forceRefreshRequests}</p>
                    </div>
                `;
            }
        }

        // Initialize
        document.getElementById('networkLog').innerHTML = 'Ready to test...';
    </script>
</body>
</html>
